{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NPi0ByoAzSHiAkBoadVS+lC8J0d6Y+FGBR61lXESffY=", "__NEXT_PREVIEW_MODE_ID": "e4591761c755defa8f78999059385551", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "479c1b23c6962a0d76c7d07adf52eb0eb962dd5156a87cc7eb02935f922a3174", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a999f93edce23884c76694f31de702e7ddb84dc912018fc6a61f50a6455bdee8"}}}, "instrumentation": null, "functions": {}}