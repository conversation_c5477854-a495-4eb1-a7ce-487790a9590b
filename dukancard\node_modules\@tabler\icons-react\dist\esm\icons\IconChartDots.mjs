/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartDots = createReactComponent("outline", "chart-dots", "IconChartDots", [["path", { "d": "M3 3v18h18", "key": "svg-0" }], ["path", { "d": "M9 9m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M19 7m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-2" }], ["path", { "d": "M14 15m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-3" }], ["path", { "d": "M10.16 10.62l2.34 2.88", "key": "svg-4" }], ["path", { "d": "M15.088 13.328l2.837 -4.586", "key": "svg-5" }]]);

export { IconChartDots as default };
//# sourceMappingURL=IconChartDots.mjs.map
