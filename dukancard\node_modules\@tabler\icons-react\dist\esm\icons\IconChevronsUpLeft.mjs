/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronsUpLeft = createReactComponent("outline", "chevrons-up-left", "IconChevronsUpLeft", [["path", { "d": "M7 15v-8h8", "key": "svg-0" }], ["path", { "d": "M11 19v-8h8", "key": "svg-1" }]]);

export { IconChevronsUpLeft as default };
//# sourceMappingURL=IconChevronsUpLeft.mjs.map
