/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleChevronsRightFilled = createReactComponent("filled", "circle-chevrons-right-filled", "IconCircleChevronsRightFilled", [["path", { "d": "M12.073 1.868c5.494 .04 9.926 4.505 9.926 10c0 5.494 -4.432 9.959 -9.926 10c-5.494 .04 -9.992 -4.36 -10.073 -9.853v-.295c.081 -5.493 4.579 -9.893 10.073 -9.852m-2.366 6.425a1 1 0 0 0 -1.414 0l-.083 .094a1 1 0 0 0 .083 1.32l2.292 2.293l-2.292 2.293a1 1 0 0 0 1.414 1.414l3 -3a1 1 0 0 0 0 -1.414zm4 0a1 1 0 0 0 -1.414 0l-.083 .094a1 1 0 0 0 .083 1.32l2.292 2.293l-2.292 2.293a1 1 0 0 0 1.414 1.414l3 -3a1 1 0 0 0 0 -1.414z", "key": "svg-0" }]]);

export { IconCircleChevronsRightFilled as default };
//# sourceMappingURL=IconCircleChevronsRightFilled.mjs.map
