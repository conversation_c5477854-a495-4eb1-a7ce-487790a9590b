/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleChevronsUp = createReactComponent("outline", "circle-chevrons-up", "IconCircleChevronsUp", [["path", { "d": "M9 15l3 -3l3 3", "key": "svg-0" }], ["path", { "d": "M9 11l3 -3l3 3", "key": "svg-1" }], ["path", { "d": "M12 21a9 9 0 1 0 -.265 0l.265 0z", "key": "svg-2" }]]);

export { IconCircleChevronsUp as default };
//# sourceMappingURL=IconCircleChevronsUp.mjs.map
