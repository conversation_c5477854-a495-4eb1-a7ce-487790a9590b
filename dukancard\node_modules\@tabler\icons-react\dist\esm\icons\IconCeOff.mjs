/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCeOff = createReactComponent("outline", "ce-off", "IconCeOff", [["path", { "d": "M6.53 6.53a6.001 6.001 0 0 0 2.47 11.47", "key": "svg-0" }], ["path", { "d": "M21 6a6 6 0 0 0 -5.927 5.061l.927 .939", "key": "svg-1" }], ["path", { "d": "M16 12h5", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]]);

export { IconCeOff as default };
//# sourceMappingURL=IconCeOff.mjs.map
