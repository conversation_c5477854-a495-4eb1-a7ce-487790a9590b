/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleCaretDownFilled = createReactComponent("filled", "circle-caret-down-filled", "IconCircleCaretDownFilled", [["path", { "d": "M17 3.34a10 10 0 1 1 -15 8.66l.005 -.324a10 10 0 0 1 14.995 -8.336m-2 6.66h-6a1 1 0 0 0 -.708 1.707l3 3a1 1 0 0 0 1.415 0l3 -3a1 1 0 0 0 0 -1.414l-.094 -.083a1 1 0 0 0 -.613 -.21", "key": "svg-0" }]]);

export { IconCircleCaretDownFilled as default };
//# sourceMappingURL=IconCircleCaretDownFilled.mjs.map
