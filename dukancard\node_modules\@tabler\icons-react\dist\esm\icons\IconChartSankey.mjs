/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartSankey = createReactComponent("outline", "chart-sankey", "IconChartSankey", [["path", { "d": "M3 3v18h18", "key": "svg-0" }], ["path", { "d": "M3 6h18", "key": "svg-1" }], ["path", { "d": "M3 8c10 0 8 9 18 9", "key": "svg-2" }]]);

export { IconChartSankey as default };
//# sourceMappingURL=IconChartSankey.mjs.map
