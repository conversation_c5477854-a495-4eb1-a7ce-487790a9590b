/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChessRook = createReactComponent("outline", "chess-rook", "IconChessRook", [["path", { "d": "M8 16l-1.447 .724a1 1 0 0 0 -.553 .894v2.382h12v-2.382a1 1 0 0 0 -.553 -.894l-1.447 -.724h-8z", "key": "svg-0" }], ["path", { "d": "M8 16l1 -9h6l1 9", "key": "svg-1" }], ["path", { "d": "M6 4l.5 3h11l.5 -3", "key": "svg-2" }], ["path", { "d": "M10 4v3", "key": "svg-3" }], ["path", { "d": "M14 4v3", "key": "svg-4" }]]);

export { IconChessRook as default };
//# sourceMappingURL=IconChessRook.mjs.map
