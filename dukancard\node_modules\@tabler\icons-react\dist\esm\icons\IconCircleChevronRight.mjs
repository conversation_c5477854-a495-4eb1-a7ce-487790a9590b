/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleChevronRight = createReactComponent("outline", "circle-chevron-right", "IconCircleChevronRight", [["path", { "d": "M11 9l3 3l-3 3", "key": "svg-0" }], ["path", { "d": "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0z", "key": "svg-1" }]]);

export { IconCircleChevronRight as default };
//# sourceMappingURL=IconCircleChevronRight.mjs.map
