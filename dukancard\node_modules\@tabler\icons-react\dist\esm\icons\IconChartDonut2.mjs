/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartDonut2 = createReactComponent("outline", "chart-donut-2", "IconChartDonut2", [["path", { "d": "M12 3v5m4 4h5", "key": "svg-0" }], ["path", { "d": "M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-1" }], ["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-2" }]]);

export { IconChartDonut2 as default };
//# sourceMappingURL=IconChartDonut2.mjs.map
