/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartScatter = createReactComponent("outline", "chart-scatter", "IconChartScatter", [["path", { "d": "M3 3v18h18", "key": "svg-0" }], ["path", { "d": "M8 15.015v.015", "key": "svg-1" }], ["path", { "d": "M16 16.015v.015", "key": "svg-2" }], ["path", { "d": "M8 7.03v.015", "key": "svg-3" }], ["path", { "d": "M12 11.03v.015", "key": "svg-4" }], ["path", { "d": "M19 11.03v.015", "key": "svg-5" }]]);

export { IconChartScatter as default };
//# sourceMappingURL=IconChartScatter.mjs.map
