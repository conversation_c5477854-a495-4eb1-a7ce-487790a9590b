/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleCaretLeft = createReactComponent("outline", "circle-caret-left", "IconCircleCaretLeft", [["path", { "d": "M9 12l4 -4v8z", "key": "svg-0" }], ["path", { "d": "M12 21a9 9 0 1 1 0 -18a9 9 0 0 1 0 18z", "key": "svg-1" }]]);

export { IconCircleCaretLeft as default };
//# sourceMappingURL=IconCircleCaretLeft.mjs.map
