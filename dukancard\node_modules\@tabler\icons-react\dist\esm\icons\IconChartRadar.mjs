/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartRadar = createReactComponent("outline", "chart-radar", "IconChartRadar", [["path", { "d": "M12 3l9.5 7l-3.5 11h-12l-3.5 -11z", "key": "svg-0" }], ["path", { "d": "M12 7.5l5.5 4l-2.5 5.5h-6.5l-2 -5.5z", "key": "svg-1" }], ["path", { "d": "M2.5 10l9.5 3l9.5 -3", "key": "svg-2" }], ["path", { "d": "M12 3v10l6 8", "key": "svg-3" }], ["path", { "d": "M6 21l6 -8", "key": "svg-4" }]]);

export { IconChartRadar as default };
//# sourceMappingURL=IconChartRadar.mjs.map
