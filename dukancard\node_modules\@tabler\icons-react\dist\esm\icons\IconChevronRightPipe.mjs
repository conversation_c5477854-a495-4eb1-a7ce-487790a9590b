/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronRightPipe = createReactComponent("outline", "chevron-right-pipe", "IconChevronRightPipe", [["path", { "d": "M6 6l6 6l-6 6", "key": "svg-0" }], ["path", { "d": "M17 5v13", "key": "svg-1" }]]);

export { IconChevronRightPipe as default };
//# sourceMappingURL=IconChevronRightPipe.mjs.map
