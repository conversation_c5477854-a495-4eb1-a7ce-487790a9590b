/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronLeftPipe = createReactComponent("outline", "chevron-left-pipe", "IconChevronLeftPipe", [["path", { "d": "M7 6v12", "key": "svg-0" }], ["path", { "d": "M18 6l-6 6l6 6", "key": "svg-1" }]]);

export { IconChevronLeftPipe as default };
//# sourceMappingURL=IconChevronLeftPipe.mjs.map
