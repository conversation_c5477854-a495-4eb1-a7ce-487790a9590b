/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartArcs3 = createReactComponent("outline", "chart-arcs-3", "IconChartArcs3", [["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M7 12a5 5 0 1 0 5 -5", "key": "svg-1" }], ["path", { "d": "M6.29 18.957a9 9 0 1 0 5.71 -15.957", "key": "svg-2" }]]);

export { IconChartArcs3 as default };
//# sourceMappingURL=IconChartArcs3.mjs.map
