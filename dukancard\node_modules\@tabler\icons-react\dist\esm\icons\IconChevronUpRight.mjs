/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronUpRight = createReactComponent("outline", "chevron-up-right", "IconChevronUpRight", [["path", { "d": "M8 8h8v8", "key": "svg-0" }]]);

export { IconChevronUpRight as default };
//# sourceMappingURL=IconChevronUpRight.mjs.map
