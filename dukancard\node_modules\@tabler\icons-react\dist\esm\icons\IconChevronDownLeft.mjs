/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronDownLeft = createReactComponent("outline", "chevron-down-left", "IconChevronDownLeft", [["path", { "d": "M8 8v8h8", "key": "svg-0" }]]);

export { IconChevronDownLeft as default };
//# sourceMappingURL=IconChevronDownLeft.mjs.map
