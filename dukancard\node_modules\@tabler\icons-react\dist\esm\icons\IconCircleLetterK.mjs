/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleLetterK = createReactComponent("outline", "circle-letter-k", "IconCircleLetterK", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M10 8v8", "key": "svg-1" }], ["path", { "d": "M14 8l-2.5 4l2.5 4", "key": "svg-2" }], ["path", { "d": "M10 12h1.5", "key": "svg-3" }]]);

export { IconCircleLetterK as default };
//# sourceMappingURL=IconCircleLetterK.mjs.map
