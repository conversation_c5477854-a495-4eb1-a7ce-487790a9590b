/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleChevronsRight = createReactComponent("outline", "circle-chevrons-right", "IconCircleChevronsRight", [["path", { "d": "M9 9l3 3l-3 3", "key": "svg-0" }], ["path", { "d": "M13 9l3 3l-3 3", "key": "svg-1" }], ["path", { "d": "M3 12a9 9 0 1 0 0 -.265l0 .265z", "key": "svg-2" }]]);

export { IconCircleChevronsRight as default };
//# sourceMappingURL=IconCircleChevronsRight.mjs.map
