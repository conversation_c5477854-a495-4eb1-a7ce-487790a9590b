/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartTreemap = createReactComponent("outline", "chart-treemap", "IconChartTreemap", [["path", { "d": "M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M12 4v16", "key": "svg-1" }], ["path", { "d": "M4 15h8", "key": "svg-2" }], ["path", { "d": "M12 12h8", "key": "svg-3" }], ["path", { "d": "M16 12v8", "key": "svg-4" }], ["path", { "d": "M16 16h4", "key": "svg-5" }]]);

export { IconChartTreemap as default };
//# sourceMappingURL=IconChartTreemap.mjs.map
