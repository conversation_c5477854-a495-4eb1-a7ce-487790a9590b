/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronCompactLeft = createReactComponent("outline", "chevron-compact-left", "IconChevronCompactLeft", [["path", { "d": "M13 20l-3 -8l3 -8", "key": "svg-0" }]]);

export { IconChevronCompactLeft as default };
//# sourceMappingURL=IconChevronCompactLeft.mjs.map
