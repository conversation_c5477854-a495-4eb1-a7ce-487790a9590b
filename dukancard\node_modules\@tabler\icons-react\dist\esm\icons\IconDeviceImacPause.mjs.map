{"version": 3, "file": "IconDeviceImacPause.mjs", "sources": ["../../../src/icons/IconDeviceImacPause.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'device-imac-pause', 'IconDeviceImacPause', [[\"path\",{\"d\":\"M13 17h-9a1 1 0 0 1 -1 -1v-12a1 1 0 0 1 1 -1h16a1 1 0 0 1 1 1v9\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M3 13h18\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M8 21h5\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M10 17l-.5 4\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M17 17v5\",\"key\":\"svg-4\"}],[\"path\",{\"d\":\"M21 17v5\",\"key\":\"svg-5\"}]]);"], "names": [], "mappings": ";;;;;;;;;AACA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mBAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAA,CAAC,CAAC,MAAA,CAAO,CAAA,CAAA,CAAC,GAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkE,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,OAAO,CAAC,CAAA,CAAE,EAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAC,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,KAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAC,CAAA,CAAA,CAAA,CAAA,CAAI,SAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAE,CAAA,CAAA,CAAC,MAAO,CAAA,CAAA,CAAA,CAAC,GAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,EAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAC,CAAA,CAAA,CAAA,CAAA,EAAI,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAC,CAAE,CAAA,CAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAC,CAAI,CAAA,CAAA,CAAA,CAAA,UAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAC,CAAA;;"}