/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleDotFilled = createReactComponent("filled", "circle-dot-filled", "IconCircleDotFilled", [["path", { "d": "M17 3.34a10 10 0 1 1 -14.995 8.984l-.005 -.324l.005 -.324a10 10 0 0 1 14.995 -8.336zm-5 6.66a2 2 0 0 0 -1.977 1.697l-.018 .154l-.005 .149l.005 .15a2 2 0 1 0 1.995 -2.15z", "key": "svg-0" }]]);

export { IconCircleDotFilled as default };
//# sourceMappingURL=IconCircleDotFilled.mjs.map
