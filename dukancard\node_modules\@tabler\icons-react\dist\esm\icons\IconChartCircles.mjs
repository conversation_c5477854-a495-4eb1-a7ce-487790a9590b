/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartCircles = createReactComponent("outline", "chart-circles", "IconChartCircles", [["path", { "d": "M9.5 9.5m-5.5 0a5.5 5.5 0 1 0 11 0a5.5 5.5 0 1 0 -11 0", "key": "svg-0" }], ["path", { "d": "M14.5 14.5m-5.5 0a5.5 5.5 0 1 0 11 0a5.5 5.5 0 1 0 -11 0", "key": "svg-1" }]]);

export { IconChartCircles as default };
//# sourceMappingURL=IconChartCircles.mjs.map
