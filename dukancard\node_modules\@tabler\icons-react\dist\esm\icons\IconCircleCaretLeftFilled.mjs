/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleCaretLeftFilled = createReactComponent("filled", "circle-caret-left-filled", "IconCircleCaretLeftFilled", [["path", { "d": "M12 2c5.523 0 10 4.477 10 10s-4.477 10 -10 10a10 10 0 1 1 0 -20m2 13v-6a1 1 0 0 0 -1.707 -.708l-3 3a1 1 0 0 0 0 1.415l3 3a1 1 0 0 0 1.414 0l.083 -.094c.14 -.18 .21 -.396 .21 -.613", "key": "svg-0" }]]);

export { IconCircleCaretLeftFilled as default };
//# sourceMappingURL=IconCircleCaretLeftFilled.mjs.map
