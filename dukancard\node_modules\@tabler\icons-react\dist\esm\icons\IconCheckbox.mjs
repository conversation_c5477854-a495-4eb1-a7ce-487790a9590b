/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCheckbox = createReactComponent("outline", "checkbox", "IconCheckbox", [["path", { "d": "M9 11l3 3l8 -8", "key": "svg-0" }], ["path", { "d": "M20 12v6a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h9", "key": "svg-1" }]]);

export { IconCheckbox as default };
//# sourceMappingURL=IconCheckbox.mjs.map
