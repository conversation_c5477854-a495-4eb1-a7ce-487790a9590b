/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronUpLeft = createReactComponent("outline", "chevron-up-left", "IconChevronUpLeft", [["path", { "d": "M8 16v-8h8", "key": "svg-0" }]]);

export { IconChevronUpLeft as default };
//# sourceMappingURL=IconChevronUpLeft.mjs.map
