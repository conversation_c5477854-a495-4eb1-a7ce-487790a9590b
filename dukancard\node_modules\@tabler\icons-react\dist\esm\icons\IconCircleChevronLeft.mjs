/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleChevronLeft = createReactComponent("outline", "circle-chevron-left", "IconCircleChevronLeft", [["path", { "d": "M13 15l-3 -3l3 -3", "key": "svg-0" }], ["path", { "d": "M21 12a9 9 0 1 0 -18 0a9 9 0 0 0 18 0z", "key": "svg-1" }]]);

export { IconCircleChevronLeft as default };
//# sourceMappingURL=IconCircleChevronLeft.mjs.map
