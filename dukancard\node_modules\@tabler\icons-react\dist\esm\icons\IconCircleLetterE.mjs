/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleLetterE = createReactComponent("outline", "circle-letter-e", "IconCircleLetterE", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M14 8h-4v8h4", "key": "svg-1" }], ["path", { "d": "M10 12h2.5", "key": "svg-2" }]]);

export { IconCircleLetterE as default };
//# sourceMappingURL=IconCircleLetterE.mjs.map
