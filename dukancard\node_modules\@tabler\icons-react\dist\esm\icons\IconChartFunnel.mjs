/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartFunnel = createReactComponent("outline", "chart-funnel", "IconChartFunnel", [["path", { "d": "M4.387 3h15.226a1 1 0 0 1 .948 1.316l-5.105 15.316a2 2 0 0 1 -1.898 1.368h-3.116a2 2 0 0 1 -1.898 -1.368l-5.104 -15.316a1 1 0 0 1 .947 -1.316", "key": "svg-0" }], ["path", { "d": "M5 9h14", "key": "svg-1" }], ["path", { "d": "M7 15h10", "key": "svg-2" }]]);

export { IconChartFunnel as default };
//# sourceMappingURL=IconChartFunnel.mjs.map
