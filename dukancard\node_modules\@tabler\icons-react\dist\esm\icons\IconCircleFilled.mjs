/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleFilled = createReactComponent("filled", "circle-filled", "IconCircleFilled", [["path", { "d": "M7 3.34a10 10 0 1 1 -4.995 8.984l-.005 -.324l.005 -.324a10 10 0 0 1 4.995 -8.336z", "key": "svg-0" }]]);

export { IconCircleFilled as default };
//# sourceMappingURL=IconCircleFilled.mjs.map
