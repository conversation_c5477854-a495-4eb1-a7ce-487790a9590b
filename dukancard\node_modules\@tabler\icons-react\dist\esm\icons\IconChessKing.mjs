/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChessKing = createReactComponent("outline", "chess-king", "IconChessKing", [["path", { "d": "M8 16l-1.447 .724a1 1 0 0 0 -.553 .894v2.382h12v-2.382a1 1 0 0 0 -.553 -.894l-1.447 -.724h-8z", "key": "svg-0" }], ["path", { "d": "M8.5 16a3.5 3.5 0 1 1 3.163 -5h.674a3.5 3.5 0 1 1 3.163 5z", "key": "svg-1" }], ["path", { "d": "M9 6h6", "key": "svg-2" }], ["path", { "d": "M12 3v8", "key": "svg-3" }]]);

export { IconChessKing as default };
//# sourceMappingURL=IconChessKing.mjs.map
