/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleChevronRightFilled = createReactComponent("filled", "circle-chevron-right-filled", "IconCircleChevronRightFilled", [["path", { "d": "M12 2c5.523 0 10 4.477 10 10a10 10 0 0 1 -20 0c0 -5.523 4.477 -10 10 -10m-.293 6.293a1 1 0 0 0 -1.414 0l-.083 .094a1 1 0 0 0 .083 1.32l2.292 2.293l-2.292 2.293a1 1 0 0 0 1.414 1.414l3 -3a1 1 0 0 0 0 -1.414z", "key": "svg-0" }]]);

export { IconCircleChevronRightFilled as default };
//# sourceMappingURL=IconCircleChevronRightFilled.mjs.map
