/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChecklist = createReactComponent("outline", "checklist", "IconChecklist", [["path", { "d": "M9.615 20h-2.615a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8", "key": "svg-0" }], ["path", { "d": "M14 19l2 2l4 -4", "key": "svg-1" }], ["path", { "d": "M9 8h4", "key": "svg-2" }], ["path", { "d": "M9 12h2", "key": "svg-3" }]]);

export { IconChecklist as default };
//# sourceMappingURL=IconChecklist.mjs.map
