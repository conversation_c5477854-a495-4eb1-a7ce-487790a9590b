/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCertificate2 = createReactComponent("outline", "certificate-2", "IconCertificate2", [["path", { "d": "M12 15m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M10 7h4", "key": "svg-1" }], ["path", { "d": "M10 18v4l2 -1l2 1v-4", "key": "svg-2" }], ["path", { "d": "M10 19h-2a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-2", "key": "svg-3" }]]);

export { IconCertificate2 as default };
//# sourceMappingURL=IconCertificate2.mjs.map
