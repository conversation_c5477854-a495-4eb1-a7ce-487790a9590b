/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleChevronUpFilled = createReactComponent("filled", "circle-chevron-up-filled", "IconCircleChevronUpFilled", [["path", { "d": "M17 3.34a10 10 0 1 1 -15 8.66l.005 -.324a10 10 0 0 1 14.995 -8.336m-4.293 5.953a1 1 0 0 0 -1.414 0l-3 3a1 1 0 0 0 0 1.414l.094 .083a1 1 0 0 0 1.32 -.083l2.293 -2.292l2.293 2.292a1 1 0 0 0 1.414 -1.414z", "key": "svg-0" }]]);

export { IconCircleChevronUpFilled as default };
//# sourceMappingURL=IconCircleChevronUpFilled.mjs.map
