/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleLetterJ = createReactComponent("outline", "circle-letter-j", "IconCircleLetterJ", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M10 8h4v6a2 2 0 1 1 -4 0", "key": "svg-1" }]]);

export { IconCircleLetterJ as default };
//# sourceMappingURL=IconCircleLetterJ.mjs.map
