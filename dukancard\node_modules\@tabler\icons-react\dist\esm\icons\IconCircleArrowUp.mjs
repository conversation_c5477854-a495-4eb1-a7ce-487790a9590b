/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleArrowUp = createReactComponent("outline", "circle-arrow-up", "IconCircleArrowUp", [["path", { "d": "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0", "key": "svg-0" }], ["path", { "d": "M12 8l-4 4", "key": "svg-1" }], ["path", { "d": "M12 8v8", "key": "svg-2" }], ["path", { "d": "M16 12l-4 -4", "key": "svg-3" }]]);

export { IconCircleArrowUp as default };
//# sourceMappingURL=IconCircleArrowUp.mjs.map
