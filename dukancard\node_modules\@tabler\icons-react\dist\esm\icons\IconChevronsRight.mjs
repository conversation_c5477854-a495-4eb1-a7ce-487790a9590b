/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronsRight = createReactComponent("outline", "chevrons-right", "IconChevronsRight", [["path", { "d": "M7 7l5 5l-5 5", "key": "svg-0" }], ["path", { "d": "M13 7l5 5l-5 5", "key": "svg-1" }]]);

export { IconChevronsRight as default };
//# sourceMappingURL=IconChevronsRight.mjs.map
