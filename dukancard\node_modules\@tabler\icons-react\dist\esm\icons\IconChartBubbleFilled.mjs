/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartBubbleFilled = createReactComponent("filled", "chart-bubble-filled", "IconChartBubbleFilled", [["path", { "d": "M6 12a4 4 0 1 1 -3.995 4.2l-.005 -.2l.005 -.2a4 4 0 0 1 3.995 -3.8z", "key": "svg-0" }], ["path", { "d": "M16 16a3 3 0 1 1 -2.995 3.176l-.005 -.176l.005 -.176a3 3 0 0 1 2.995 -2.824z", "key": "svg-1" }], ["path", { "d": "M14.5 2a5.5 5.5 0 1 1 -5.496 5.721l-.004 -.221l.004 -.221a5.5 5.5 0 0 1 5.496 -5.279z", "key": "svg-2" }]]);

export { IconChartBubbleFilled as default };
//# sourceMappingURL=IconChartBubbleFilled.mjs.map
