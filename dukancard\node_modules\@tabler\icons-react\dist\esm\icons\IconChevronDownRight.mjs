/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronDownRight = createReactComponent("outline", "chevron-down-right", "IconChevronDownRight", [["path", { "d": "M16 8v8h-8", "key": "svg-0" }]]);

export { IconChevronDownRight as default };
//# sourceMappingURL=IconChevronDownRight.mjs.map
