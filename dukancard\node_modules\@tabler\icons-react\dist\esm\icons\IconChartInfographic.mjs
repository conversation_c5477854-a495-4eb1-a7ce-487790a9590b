/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartInfographic = createReactComponent("outline", "chart-infographic", "IconChartInfographic", [["path", { "d": "M7 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-0" }], ["path", { "d": "M7 3v4h4", "key": "svg-1" }], ["path", { "d": "M9 17l0 4", "key": "svg-2" }], ["path", { "d": "M17 14l0 7", "key": "svg-3" }], ["path", { "d": "M13 13l0 8", "key": "svg-4" }], ["path", { "d": "M21 12l0 9", "key": "svg-5" }]]);

export { IconChartInfographic as default };
//# sourceMappingURL=IconChartInfographic.mjs.map
