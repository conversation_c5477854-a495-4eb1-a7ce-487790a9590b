/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartPie2Filled = createReactComponent("filled", "chart-pie-2-filled", "IconChartPie2Filled", [["path", { "d": "M11 2.05v9.95a1 1 0 0 0 1 1h9.95a10 10 0 0 1 -19.95 -1l.005 -.324a10 10 0 0 1 8.995 -9.626m6 1.29a10 10 0 0 1 4.95 7.66h-8.95v-8.95a10 10 0 0 1 4 1.29", "key": "svg-0" }]]);

export { IconChartPie2Filled as default };
//# sourceMappingURL=IconChartPie2Filled.mjs.map
