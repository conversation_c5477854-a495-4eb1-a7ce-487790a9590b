/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleCaretRightFilled = createReactComponent("filled", "circle-caret-right-filled", "IconCircleCaretRightFilled", [["path", { "d": "M17 3.34a10 10 0 1 1 -15 8.66l.005 -.324a10 10 0 0 1 14.995 -8.336m-5.293 4.953a1 1 0 0 0 -1.707 .707v6c0 .217 .07 .433 .21 .613l.083 .094a1 1 0 0 0 1.414 0l3 -3a1 1 0 0 0 0 -1.414z", "key": "svg-0" }]]);

export { IconCircleCaretRightFilled as default };
//# sourceMappingURL=IconCircleCaretRightFilled.mjs.map
