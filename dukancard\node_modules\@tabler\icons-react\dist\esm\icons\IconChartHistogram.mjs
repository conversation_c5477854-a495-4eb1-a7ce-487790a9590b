/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartHistogram = createReactComponent("outline", "chart-histogram", "IconChartHistogram", [["path", { "d": "M3 3v18h18", "key": "svg-0" }], ["path", { "d": "M20 18v3", "key": "svg-1" }], ["path", { "d": "M16 16v5", "key": "svg-2" }], ["path", { "d": "M12 13v8", "key": "svg-3" }], ["path", { "d": "M8 16v5", "key": "svg-4" }], ["path", { "d": "M3 11c6 0 5 -5 9 -5s3 5 9 5", "key": "svg-5" }]]);

export { IconChartHistogram as default };
//# sourceMappingURL=IconChartHistogram.mjs.map
