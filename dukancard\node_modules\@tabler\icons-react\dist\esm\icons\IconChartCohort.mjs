/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartCohort = createReactComponent("outline", "chart-cohort", "IconChartCohort", [["path", { "d": "M3 9h18v-6h-18v18h6v-18", "key": "svg-0" }], ["path", { "d": "M3 15h12v-12", "key": "svg-1" }]]);

export { IconChartCohort as default };
//# sourceMappingURL=IconChartCohort.mjs.map
