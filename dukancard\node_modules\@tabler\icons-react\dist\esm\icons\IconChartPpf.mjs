/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartPpf = createReactComponent("outline", "chart-ppf", "IconChartPpf", [["path", { "d": "M19 17c0 -6.075 -5.373 -11 -12 -11", "key": "svg-0" }], ["path", { "d": "M3 3v18h18", "key": "svg-1" }]]);

export { IconChartPpf as default };
//# sourceMappingURL=IconChartPpf.mjs.map
