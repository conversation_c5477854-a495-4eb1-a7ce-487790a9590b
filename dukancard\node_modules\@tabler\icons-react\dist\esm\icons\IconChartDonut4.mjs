/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartDonut4 = createReactComponent("outline", "chart-donut-4", "IconChartDonut4", [["path", { "d": "M8.848 14.667l-3.348 2.833", "key": "svg-0" }], ["path", { "d": "M12 3v5m4 4h5", "key": "svg-1" }], ["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-2" }], ["path", { "d": "M14.219 15.328l2.781 4.172", "key": "svg-3" }], ["path", { "d": "M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0", "key": "svg-4" }]]);

export { IconChartDonut4 as default };
//# sourceMappingURL=IconChartDonut4.mjs.map
