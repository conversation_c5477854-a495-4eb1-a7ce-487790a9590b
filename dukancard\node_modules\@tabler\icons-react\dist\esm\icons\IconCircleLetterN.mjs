/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleLetterN = createReactComponent("outline", "circle-letter-n", "IconCircleLetterN", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M10 16v-8l4 8v-8", "key": "svg-1" }]]);

export { IconCircleLetterN as default };
//# sourceMappingURL=IconCircleLetterN.mjs.map
