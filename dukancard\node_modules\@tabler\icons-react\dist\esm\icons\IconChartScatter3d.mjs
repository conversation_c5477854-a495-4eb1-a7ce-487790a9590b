/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartScatter3d = createReactComponent("outline", "chart-scatter-3d", "IconChartScatter3d", [["path", { "d": "M3 20l9 -7", "key": "svg-0" }], ["path", { "d": "M12 3v10l9 7", "key": "svg-1" }], ["path", { "d": "M17 12v.015", "key": "svg-2" }], ["path", { "d": "M17 4.015v.015", "key": "svg-3" }], ["path", { "d": "M21 8.015v.015", "key": "svg-4" }], ["path", { "d": "M12 19.015v.015", "key": "svg-5" }], ["path", { "d": "M3 12.015v.015", "key": "svg-6" }], ["path", { "d": "M7 8.015v.015", "key": "svg-7" }], ["path", { "d": "M3 4.015v.015", "key": "svg-8" }]]);

export { IconChartScatter3d as default };
//# sourceMappingURL=IconChartScatter3d.mjs.map
