"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";

export interface UsePostOwnershipProps {
  postBusinessId?: string;
  postCustomerId?: string;
  postSource?: "business" | "customer";
  // Legacy support - if only postBusinessId is provided, assume it's the author_id from unified posts
  authorId?: string;
}

export interface UsePostOwnershipReturn {
  isOwner: boolean;
  isLoading: boolean;
  currentUserId: string | null;
}

/**
 * Hook to determine if the current user owns a specific post
 * Supports both business and customer posts with proper ownership checking
 */
export function usePostOwnership({
  postBusinessId,
  postCustomerId,
  postSource,
  authorId,
}: UsePostOwnershipProps): UsePostOwnershipReturn {
  const [isOwner, setIsOwner] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    const checkOwnership = async () => {
      try {
        const supabase = createClient();
        const {
          data: { user },
          error,
        } = await supabase.auth.getUser();

        if (error || !user) {
          setIsOwner(false);
          setCurrentUserId(null);
          setIsLoading(false);
          return;
        }

        setCurrentUserId(user.id);

        // If authorId is provided (from unified posts), use it directly
        if (authorId) {
          setIsOwner(user.id === authorId);
        }
        // Otherwise, check ownership based on post source
        else if (postSource === "business") {
          // For business posts, check if current user's ID matches the post's business_id
          setIsOwner(!!(postBusinessId && user.id === postBusinessId));
        } else if (postSource === "customer") {
          // For customer posts, check if current user's ID matches the post's customer_id
          setIsOwner(!!(postCustomerId && user.id === postCustomerId));
        }
        // Legacy fallback - assume postBusinessId is the author ID
        else if (postBusinessId) {
          setIsOwner(user.id === postBusinessId);
        } else {
          setIsOwner(false);
        }
      } catch (error) {
        console.error("Error checking post ownership:", error);
        setIsOwner(false);
        setCurrentUserId(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkOwnership();
  }, [postBusinessId, postCustomerId, postSource, authorId]);

  return {
    isOwner,
    isLoading,
    currentUserId,
  };
}

/**
 * Simple utility function to check if user owns a post
 */
export function checkPostOwnership(
  currentUserId: string | null,
  postBusinessId: string | undefined,
  postCustomerId?: string | undefined,
  postSource?: "business" | "customer",
  authorId?: string
): boolean {
  if (!currentUserId) return false;

  // If authorId is provided (from unified posts), use it directly
  if (authorId) {
    return currentUserId === authorId;
  }

  // Otherwise, check based on post source
  if (postSource === "business") {
    return !!(postBusinessId && currentUserId === postBusinessId);
  } else if (postSource === "customer") {
    return !!(postCustomerId && currentUserId === postCustomerId);
  }

  // Legacy fallback
  return !!(postBusinessId && currentUserId === postBusinessId);
}
