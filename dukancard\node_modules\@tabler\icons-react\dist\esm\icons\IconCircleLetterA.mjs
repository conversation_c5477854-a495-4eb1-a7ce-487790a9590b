/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleLetterA = createReactComponent("outline", "circle-letter-a", "IconCircleLetterA", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M10 16v-6a2 2 0 1 1 4 0v6", "key": "svg-1" }], ["path", { "d": "M10 13h4", "key": "svg-2" }]]);

export { IconCircleLetterA as default };
//# sourceMappingURL=IconCircleLetterA.mjs.map
