/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleArrowDownLeft = createReactComponent("outline", "circle-arrow-down-left", "IconCircleArrowDownLeft", [["path", { "d": "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0", "key": "svg-0" }], ["path", { "d": "M15 9l-6 6", "key": "svg-1" }], ["path", { "d": "M15 15h-6v-6", "key": "svg-2" }]]);

export { IconCircleArrowDownLeft as default };
//# sourceMappingURL=IconCircleArrowDownLeft.mjs.map
