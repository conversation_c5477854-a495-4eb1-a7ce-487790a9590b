/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartLine = createReactComponent("outline", "chart-line", "IconChartLine", [["path", { "d": "M4 19l16 0", "key": "svg-0" }], ["path", { "d": "M4 15l4 -6l4 2l4 -5l4 4", "key": "svg-1" }]]);

export { IconChartLine as default };
//# sourceMappingURL=IconChartLine.mjs.map
