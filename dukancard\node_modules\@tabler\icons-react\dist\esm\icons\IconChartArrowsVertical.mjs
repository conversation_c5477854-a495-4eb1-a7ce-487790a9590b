/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartArrowsVertical = createReactComponent("outline", "chart-arrows-vertical", "IconChartArrowsVertical", [["path", { "d": "M18 21v-14", "key": "svg-0" }], ["path", { "d": "M9 15l3 -3l3 3", "key": "svg-1" }], ["path", { "d": "M15 10l3 -3l3 3", "key": "svg-2" }], ["path", { "d": "M3 21l18 0", "key": "svg-3" }], ["path", { "d": "M12 21l0 -9", "key": "svg-4" }], ["path", { "d": "M3 6l3 -3l3 3", "key": "svg-5" }], ["path", { "d": "M6 21v-18", "key": "svg-6" }]]);

export { IconChartArrowsVertical as default };
//# sourceMappingURL=IconChartArrowsVertical.mjs.map
