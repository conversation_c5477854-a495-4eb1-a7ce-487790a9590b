/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronUp = createReactComponent("outline", "chevron-up", "IconChevronUp", [["path", { "d": "M6 15l6 -6l6 6", "key": "svg-0" }]]);

export { IconChevronUp as default };
//# sourceMappingURL=IconChevronUp.mjs.map
