/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleLetterIFilled = createReactComponent("filled", "circle-letter-i-filled", "IconCircleLetterIFilled", [["path", { "d": "M12 2c5.523 0 10 4.477 10 10s-4.477 10 -10 10s-10 -4.477 -10 -10s4.477 -10 10 -10m0 5a1 1 0 0 0 -1 1v8a1 1 0 0 0 2 0v-8a1 1 0 0 0 -1 -1", "key": "svg-0" }]]);

export { IconCircleLetterIFilled as default };
//# sourceMappingURL=IconCircleLetterIFilled.mjs.map
