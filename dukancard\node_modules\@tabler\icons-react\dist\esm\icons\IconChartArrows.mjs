/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartArrows = createReactComponent("outline", "chart-arrows", "IconChartArrows", [["path", { "d": "M3 18l14 0", "key": "svg-0" }], ["path", { "d": "M9 9l3 3l-3 3", "key": "svg-1" }], ["path", { "d": "M14 15l3 3l-3 3", "key": "svg-2" }], ["path", { "d": "M3 3l0 18", "key": "svg-3" }], ["path", { "d": "M3 12l9 0", "key": "svg-4" }], ["path", { "d": "M18 3l3 3l-3 3", "key": "svg-5" }], ["path", { "d": "M3 6l18 0", "key": "svg-6" }]]);

export { IconChart<PERSON>rrows as default };
//# sourceMappingURL=IconChartArrows.mjs.map
