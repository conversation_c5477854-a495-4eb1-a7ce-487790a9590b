/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleLetterM = createReactComponent("outline", "circle-letter-m", "IconCircleLetterM", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M9 16v-8l3 5l3 -5v8", "key": "svg-1" }]]);

export { IconCircleLetterM as default };
//# sourceMappingURL=IconCircleLetterM.mjs.map
