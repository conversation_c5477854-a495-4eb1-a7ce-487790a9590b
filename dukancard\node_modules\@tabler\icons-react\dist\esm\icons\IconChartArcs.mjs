/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartArcs = createReactComponent("outline", "chart-arcs", "IconChartArcs", [["path", { "d": "M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0", "key": "svg-0" }], ["path", { "d": "M16.924 11.132a5 5 0 1 0 -4.056 5.792", "key": "svg-1" }], ["path", { "d": "M3 12a9 9 0 1 0 9 -9", "key": "svg-2" }]]);

export { IconChartArcs as default };
//# sourceMappingURL=IconChartArcs.mjs.map
