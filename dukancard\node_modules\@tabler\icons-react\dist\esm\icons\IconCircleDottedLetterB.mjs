/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleDottedLetterB = createReactComponent("outline", "circle-dotted-letter-b", "IconCircleDottedLetterB", [["path", { "d": "M10 16h2a2 2 0 1 0 0 -4h-2h2a2 2 0 1 0 0 -4h-2z", "key": "svg-0" }], ["path", { "d": "M7.5 4.21v.01", "key": "svg-1" }], ["path", { "d": "M4.21 7.5v.01", "key": "svg-2" }], ["path", { "d": "M3 12v.01", "key": "svg-3" }], ["path", { "d": "M4.21 16.5v.01", "key": "svg-4" }], ["path", { "d": "M7.5 19.79v.01", "key": "svg-5" }], ["path", { "d": "M12 21v.01", "key": "svg-6" }], ["path", { "d": "M16.5 19.79v.01", "key": "svg-7" }], ["path", { "d": "M19.79 16.5v.01", "key": "svg-8" }], ["path", { "d": "M21 12v.01", "key": "svg-9" }], ["path", { "d": "M19.79 7.5v.01", "key": "svg-10" }], ["path", { "d": "M16.5 4.21v.01", "key": "svg-11" }], ["path", { "d": "M12 3v.01", "key": "svg-12" }]]);

export { IconCircleDottedLetterB as default };
//# sourceMappingURL=IconCircleDottedLetterB.mjs.map
