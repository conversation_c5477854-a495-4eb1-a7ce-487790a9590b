/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleKey = createReactComponent("outline", "circle-key", "IconCircleKey", [["path", { "d": "M14 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M21 12a9 9 0 1 1 -18 0a9 9 0 0 1 18 0z", "key": "svg-1" }], ["path", { "d": "M12.5 11.5l-4 4l1.5 1.5", "key": "svg-2" }], ["path", { "d": "M12 15l-1.5 -1.5", "key": "svg-3" }]]);

export { IconCircleKey as default };
//# sourceMappingURL=IconCircleKey.mjs.map
