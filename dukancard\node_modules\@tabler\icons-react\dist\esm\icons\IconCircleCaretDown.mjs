/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCircleCaretDown = createReactComponent("outline", "circle-caret-down", "IconCircleCaretDown", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M12 15l-4 -4h8z", "key": "svg-1" }]]);

export { IconCircleCaretDown as default };
//# sourceMappingURL=IconCircleCaretDown.mjs.map
