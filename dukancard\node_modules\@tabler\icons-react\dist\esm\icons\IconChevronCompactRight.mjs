/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronCompactRight = createReactComponent("outline", "chevron-compact-right", "IconChevronCompactRight", [["path", { "d": "M11 4l3 8l-3 8", "key": "svg-0" }]]);

export { IconChevronCompactRight as default };
//# sourceMappingURL=IconChevronCompactRight.mjs.map
