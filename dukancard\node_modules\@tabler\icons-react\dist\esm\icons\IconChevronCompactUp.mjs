/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChevronCompactUp = createReactComponent("outline", "chevron-compact-up", "IconChevronCompactUp", [["path", { "d": "M4 13l8 -3l8 3", "key": "svg-0" }]]);

export { IconChevronCompactUp as default };
//# sourceMappingURL=IconChevronCompactUp.mjs.map
