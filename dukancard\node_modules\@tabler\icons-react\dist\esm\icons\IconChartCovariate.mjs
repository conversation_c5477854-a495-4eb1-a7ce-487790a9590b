/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChartCovariate = createReactComponent("outline", "chart-covariate", "IconChartCovariate", [["path", { "d": "M18 11h.009", "key": "svg-0" }], ["path", { "d": "M14 15h.009", "key": "svg-1" }], ["path", { "d": "M12 6h.009", "key": "svg-2" }], ["path", { "d": "M8 10h.009", "key": "svg-3" }], ["path", { "d": "M3 21l17 -17", "key": "svg-4" }], ["path", { "d": "M3 3v18h18", "key": "svg-5" }]]);

export { IconChartCovariate as default };
//# sourceMappingURL=IconChartCovariate.mjs.map
