/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconChefHat = createReactComponent("outline", "chef-hat", "IconChefHat", [["path", { "d": "M12 3c1.918 0 3.52 1.35 3.91 3.151a4 4 0 0 1 2.09 7.723l0 7.126h-12v-7.126a4 4 0 1 1 2.092 -7.723a4 4 0 0 1 3.908 -3.151z", "key": "svg-0" }], ["path", { "d": "M6.161 17.009l11.839 -.009", "key": "svg-1" }]]);

export { IconChefHat as default };
//# sourceMappingURL=IconChefHat.mjs.map
